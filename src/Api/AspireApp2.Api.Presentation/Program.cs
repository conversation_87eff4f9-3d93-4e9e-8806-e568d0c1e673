using System.Reflection;
using AspireApp2.Api.Application.Commands.Players;
using AspireApp2.Api.Application.Mappings;
using AspireApp2.Api.Infrastructure;
using AspireApp2.ServiceDefaults;
using Confluent.Kafka;
using FluentValidation;
using FluentValidation.AspNetCore;
using Microsoft.OpenApi.Models;

WebApplicationBuilder builder = WebApplication.CreateBuilder(args);

// Add service defaults & Aspire client integrations.
builder.AddServiceDefaults();

// Add Kafka producer
builder.AddKafkaProducer<string, string>("kafka", settings =>
{
    settings.Config.Acks = Acks.All;
    settings.Config.EnableIdempotence = true;
    settings.Config.MessageTimeoutMs = 30_000;
    settings.Config.RetryBackoffMs = 100;
    settings.Config.MessageSendMaxRetries = 3;
    settings.Config.CompressionType = CompressionType.Snappy;
    settings.Config.ClientId = $"{Environment.MachineName}-api-producer";
    settings.Config.BrokerAddressFamily = BrokerAddressFamily.V4;
});

// Add services to the container.
builder.Services.AddControllers();

// Add MediatR
builder.Services.AddMediatR(cfg =>
{
    cfg.RegisterServicesFromAssembly(typeof(CreatePlayerCommand).Assembly);
});

// Add FluentValidation
builder.Services.AddFluentValidationAutoValidation();
builder.Services.AddValidatorsFromAssembly(typeof(CreatePlayerCommand).Assembly);

// Add AutoMapper
builder.Services.AddAutoMapper(typeof(PlayerMappingProfile).Assembly);

// Add Infrastructure services
builder.Services.AddInfrastructure(builder.Configuration);

// Add OpenAPI/Swagger
builder.Services.AddOpenApi();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1",
        new OpenApiInfo
        {
            Title = "AspireApp2 Clean Architecture API",
            Version = "v1",
            Description = "A clean architecture implementation of the AspireApp2 API"
        });

    // Include XML comments
    string xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    string xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        c.IncludeXmlComments(xmlPath);
    }
});

// Add CORS
builder.Services.AddCors(options =>
{
    options.AddDefaultPolicy(policy =>
    {
        policy.AllowAnyOrigin()
            .AllowAnyMethod()
            .AllowAnyHeader();
    });
});

WebApplication app = builder.Build();

// Configure the HTTP request pipeline.
if (app.Environment.IsDevelopment())
{
    app.MapOpenApi();
    app.UseSwagger();
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "AspireApp2 Clean Architecture API v1");
        c.RoutePrefix = string.Empty; // Serve Swagger UI at root
    });
}

app.UseHttpsRedirection();
app.UseCors();

app.MapControllers();

// Map default endpoints (health checks, etc.)
app.MapDefaultEndpoints();

app.Run();

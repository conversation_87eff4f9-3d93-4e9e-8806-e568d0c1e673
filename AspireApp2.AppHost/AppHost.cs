using Projects;

IDistributedApplicationBuilder builder = DistributedApplication.CreateBuilder(args);

// Add infrastructure services with better resource allocation
IResourceBuilder<RedisResource> cache = builder.AddRedis("cache")
    .WithDataVolume();

IResourceBuilder<SqlServerDatabaseResource> sqlServer = builder.AddSqlServer("sql")
    .WithDataVolume()
    .AddDatabase("DefaultConnection");

IResourceBuilder<MongoDBDatabaseResource> mongodb = builder.AddMongoDB("mongodb")
    .WithDataVolume()
    .AddDatabase("PlayerEvents");

// Add Kafka with enhanced configuration
IResourceBuilder<KafkaServerResource> kafka = builder.AddKafka("kafka")
    .WithKafkaUI(); // Kafka UI for monitoring

// API Service with enhanced resilience
IResourceBuilder<ProjectResource> apiService = builder.AddProject<Projects.AspireApp2_Api_Presentation>("apiservice")
    .WithReference(sqlServer)
    .WithReference(kafka)
    .WithReference(cache)
    .WithHttpHealthCheck("/health")
    .WaitFor(sqlServer)
    .WaitFor(kafka)
    .WaitFor(cache);

// Player Event Service with enhanced configuration
IResourceBuilder<ProjectResource> playerEventService = builder
    .AddProject<AspireApp2_PlayerEventService>("playereventservice")
    .WithReference(mongodb)
    .WithReference(kafka)
    .WithHttpHealthCheck("/health")
    .WaitFor(mongodb)
    .WaitFor(kafka);

// Web Frontend with enhanced configuration
builder.AddProject<AspireApp2_Web>("webfrontend")
    .WithExternalHttpEndpoints()
    .WithHttpHealthCheck("/health")
    .WithReference(cache)
    .WithReference(apiService)
    .WaitFor(cache)
    .WaitFor(apiService);

builder.Build().Run();

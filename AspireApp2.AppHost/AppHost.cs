using Projects;

IDistributedApplicationBuilder builder = DistributedApplication.CreateBuilder(args);

// Add infrastructure services with better resource allocation
IResourceBuilder<RedisResource> cache = builder.AddRedis("cache")
    .WithDataVolume("redis-data")
    .WithLifetime(ContainerLifetime.Persistent);

IResourceBuilder<SqlServerDatabaseResource> sqlServer = builder.AddSqlServer("sql")
    .WithDataVolume("sql-data")
    .WithLifetime(ContainerLifetime.Persistent)
    .AddDatabase("DefaultConnection");

IResourceBuilder<MongoDBDatabaseResource> mongodb = builder.AddMongoDB("mongodb")
    .WithDataVolume("mongo-data")
    .WithLifetime(ContainerLifetime.Persistent)
    .AddDatabase("PlayerEvents");

// Add Kafka with enhanced configuration
IResourceBuilder<KafkaServerResource> kafka = builder.AddKafka("kafka")
    .WithDataVolume("kafka-data")
    .WithLifetime(ContainerLifetime.Persistent)
    .WithKafkaUI() // Kafka UI for monitoring
    .WithEnvironment("KAFKA_AUTO_CREATE_TOPICS_ENABLE", "true")
    .WithEnvironment("KAFKA_DEFAULT_REPLICATION_FACTOR", "1")
    .WithEnvironment("KAFKA_MIN_INSYNC_REPLICAS", "1");

// Add observability stack
var grafana = builder.AddContainer("grafana", "grafana/grafana")
    .WithBindMount("./grafana", "/etc/grafana")
    .WithEndpoint(targetPort: 3000, scheme: "http", name: "grafana");

var prometheus = builder.AddContainer("prometheus", "prom/prometheus")
    .WithBindMount("./prometheus", "/etc/prometheus")
    .WithEndpoint(targetPort: 9090, scheme: "http", name: "prometheus");

// API Service with enhanced resilience
IResourceBuilder<ProjectResource> apiService = builder.AddProject<AspireApp2_Api_Presentation>("apiservice")
    .WithReference(sqlServer)
    .WithReference(kafka)
    .WithReference(cache)
    .WithHttpHealthCheck("/health")
    .WaitFor(sqlServer)
    .WaitFor(kafka)
    .WaitFor(cache)
    .WithReplicas(2) // For high availability
    .WithEnvironment("ASPNETCORE_ENVIRONMENT", builder.Environment.EnvironmentName);

// Player Event Service with enhanced configuration
IResourceBuilder<ProjectResource> playerEventService = builder
    .AddProject<AspireApp2_PlayerEventService>("playereventservice")
    .WithReference(mongodb)
    .WithReference(kafka)
    .WithHttpHealthCheck("/health")
    .WaitFor(mongodb)
    .WaitFor(kafka)
    .WithReplicas(2) // For processing resilience
    .WithEnvironment("ASPNETCORE_ENVIRONMENT", builder.Environment.EnvironmentName);

// Web Frontend with enhanced configuration
builder.AddProject<AspireApp2_Web>("webfrontend")
    .WithExternalHttpEndpoints()
    .WithHttpHealthCheck("/health")
    .WithReference(cache)
    .WithReference(apiService)
    .WaitFor(cache)
    .WaitFor(apiService)
    .WithEnvironment("ASPNETCORE_ENVIRONMENT", builder.Environment.EnvironmentName);

// Add service-to-service dependencies for proper startup ordering
apiService.WaitForCompletion(sqlServer, mongodb);
playerEventService.WaitForCompletion(mongodb, kafka);

builder.Build().Run();
